import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/data/models/user_message_model.dart';
import 'package:flutter_metatel/app/modules/message/channelMoment/channel_moment_controller.dart';
import 'package:flutter_metatel/app/modules/message/channelMoment/components/post_widget.dart';
import 'package:flutter_metatel/app/modules/message/channelMoment/components/shimmer_post_widget.dart';
import 'package:flutter_metatel/core/languages/l.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_metatel/r.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../core/utils/util.dart';
import '../../../data/models/post.dart';

class ChannelMomentView extends StatefulWidget {
  const ChannelMomentView({super.key});

  @override
  State<ChannelMomentView> createState() => _ChannelMomentViewState();
}

class _ChannelMomentViewState extends State<ChannelMomentView> {
  final ChannelMomentController _controller =
      Get.put(ChannelMomentController());
  // var top = 0.0;

  @override
  Widget build(BuildContext context) {
    double statusBarHeight = MediaQuery.of(context).padding.top;
    double _flexibleSpaceHeight = 248.r;
    return Scaffold(
      backgroundColor: AppColors.backgroundGray,
      body: Stack(
        children: [
          // Background color
          Container(
            width: double.infinity,
            height: double.infinity,
            // color: AppColors.colorFFF8F8F8,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(R.momentBg),
                fit: BoxFit.cover,
              ),
            ),
          ),
          // Main Body
          NestedScrollView(
            scrollBehavior: ScrollBehavior().copyWith(overscroll: false),
            controller: _controller.scrollController,
            physics: _controller.moments.isEmpty
                ? NeverScrollableScrollPhysics()
                : BouncingScrollPhysics(),                
            headerSliverBuilder: (context, innerBoxIsScrolled) {
              return [
                SliverToBoxAdapter(child: SizedBox(height: statusBarHeight,)),
                SliverAppBar(
                  // expandedHeight: kToolbarHeight + (248.r - kToolbarHeight),                  
                  automaticallyImplyLeading: false, // remove back button
                  expandedHeight: _flexibleSpaceHeight,
                  floating: true,
                  snap: true,
                  pinned: true,              
                  elevation: 0,
                  backgroundColor: AppColors.transparent,
                  // backgroundColor: Colors.red,
                  // leading: const BackButton(color: AppColors.white),
                  // leading: Obx(
                  //   () => AnimatedOpacity(
                  //     duration: Duration(milliseconds: 500),
                  //     opacity: _controller.isAppBarCollapsed.value  ? 1.0 : 0.0,
                  //     child: GestureDetector(
                  //       behavior: HitTestBehavior.opaque,
                  //       onTap: () => Get.back(),
                  //       child: Padding(
                  //         padding: const EdgeInsets.only(left: 5, right: 9, top: 9, bottom: 9).r,
                  //         child: Icon(
                  //           Icons.arrow_back_ios_new, 
                  //           size: 14.r,
                  //           color: AppColors.colorFFC2AF8C,
                  //         ),
                  //       ),
                  //     ),
                  //   ),
                  // ),
                  // centerTitle: innerBoxIsScrolled ? false : true,
                  centerTitle: false,

                  title: 
                      innerBoxIsScrolled
                          ? _buildCollapsedAppBarContent(
                              currentUser: _controller.currentUser,
                              memberCount: _controller.memberCount,
                            )
                          : const SizedBox.shrink(),
                    
                      // Text(
                      //     L.channel_moment_title.tr,
                      //     style: TextStyle(
                      //       color: AppColors.white, 
                      //       fontWeight: FontWeight.bold,
                      //     ),
                      //   ),
                  flexibleSpace: LayoutBuilder(
                    builder: (context, constraints) {
                      double top = constraints.biggest.height;
                      if(top <= _flexibleSpaceHeight - 20.r) {
                        if(_controller.showExpanded.value == false) {
                          _controller.showExpanded.value = true;
                        }
                      } else {
                        if(_controller.showExpanded.value == true) {
                          _controller.showExpanded.value = false;
                        }
                      }
                      return Container(
                        decoration: BoxDecoration(
                          image: DecorationImage(image: AssetImage(R.momentHeaderBoard), fit: BoxFit.fill),
                          // gradient: _getAppBarGradient(),
                          // borderRadius:innerBoxIsScrolled
                          //   ? BorderRadius.zero
                          //   : _getAppBarBorderRadius(),
                        ),
                        child: FlexibleSpaceBar(
                          collapseMode: CollapseMode.pin,
                          background: AnimatedOpacity(
                            duration: Duration(milliseconds: 500),
                            opacity: _controller.showExpanded.value  ? 0.0 : 1.0,
                            child: _buildExpandedAppBarContent(
                              currentUser: _controller.currentUser,
                              memberCount: _controller.memberCount, 
                              toolbarHeight: kToolbarHeight + statusBarHeight,              
                            ),
                          ),
                        ),
                      );
                    }
                  ),
                ),
              ];
            },
            body: Obx(() => _buildBody()),
          ),
          Column(
            children: [
              Container(),
            ],
          ),
          // Floating Action Bar
          Obx(
            () => Visibility(
              visible: _controller.isAdminOrOwner(),
              child: Positioned(
                right: 16.r,
                bottom: 60.r,
                child: _buildCreatePostBtn(
                  onTap: _controller.onCreatePostTap,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCreatePostBtn({Function()? onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 9.r, horizontal: 10.r),
        decoration: BoxDecoration(
          color: AppColors.primaryBgColor1,
          borderRadius: BorderRadius.circular(18.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              spreadRadius: 2,
              blurRadius: 10,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.add,
              size: 20.r,
              color: AppColors.white,
            ),
            SizedBox(width: 3.r),
            Text(
              L.create_post.tr,
              style: TextStyle(
                color: AppColors.white,
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCollapsedAppBarContent(
      {required UserMessage? currentUser, required int? memberCount}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        buildChatAvatarWithAttr(
          currentUser?.chatType ?? 0,
          currentUser?.userName ?? "",
          diameter: 30.r,
          imagePath: currentUser?.avatarPath ?? "",
        ),
        SizedBox(width: 6.w),
        ConstrainedBox(
          constraints: BoxConstraints(maxWidth: 0.6.sw),
          child: Text(
            currentUser?.displayName ?? "",
            style: TextStyle(
              color: AppColors.white,
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        SizedBox(width: 10.w),
        Text(
          "(${memberCount ?? ""})",
          style: TextStyle(
            color: AppColors.white.withOpacity(0.8),
            fontSize: 12.sp,
          ),
        ),
      ],
    );
  }

  Widget _buildExpandedAppBarContent(
      {required UserMessage? currentUser, required int? memberCount, required double toolbarHeight}) {
    return Container(
      padding: EdgeInsets.only(right: 28.r),
      // decoration: BoxDecoration(
      //   gradient: _getAppBarGradient(),
      //   borderRadius: _getAppBarBorderRadius(),
      // ),
      child: Column(
        children: [
          SizedBox(height: 42.r),
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [

                /// Back Button 
                Container(
                  width: 41.r,
                  child: GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () => Get.back(),
                    child: Padding(
                      padding: const EdgeInsets.only(left: 5, right: 9, top: 9, bottom: 9).r,
                      child: Icon(
                        Icons.arrow_back_ios_new, 
                        size: 14.r,
                        color: AppColors.colorFFC2AF8C,
                      ),
                    ),
                  ),
                ),

                Expanded(
                  child: Column(
                    children: [

                      /// 群头像，群名，编辑群描述按钮
                      Obx(
                        () => Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [ 
                        
                            /// 群头像          
                            Container(
                              width: 66.r,
                              height: 66.r,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                image: DecorationImage(image: AssetImage(R.joinPageAvatarFrame), fit: BoxFit.fill),
                              ),
                              child: Center(
                                child: buildChatAvatarWithAttr(
                                  currentUser?.chatType ?? 0,
                                  currentUser?.userName ?? "",
                                  diameter: 52.r,
                                  imagePath: currentUser?.avatarPath ?? "",
                                ),
                              ),
                            ),
                            SizedBox(width: 12.r),
                        
                            /// 群名
                            Expanded(
                              child: Text(
                                currentUser?.displayName ?? "",
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  color: AppColors.colorFFCAB692,
                                  fontSize: 23.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            // Column(
                            //   mainAxisAlignment: MainAxisAlignment.center,
                            //   crossAxisAlignment: CrossAxisAlignment.start,
                            //   children: [
                            //     ConstrainedBox(
                            //       constraints: BoxConstraints(maxWidth: 0.65.sw),
                            //       child: Text(
                            //         currentUser?.displayName ?? "",
                            //         maxLines: 1,
                            //         overflow: TextOverflow.ellipsis,
                            //         style: TextStyle(
                            //           color: AppColors.white,
                            //           fontSize: 16.sp,
                            //           fontWeight: FontWeight.normal,
                            //         ),
                            //       ),
                            //     ),
                            //     SizedBox(height: 2.h),
                            //     // Row(
                            //     //   children: [
                            //     //     SizedBox(width: 5.w),
                            //     //     Icon(
                            //     //       Icons.person_outlined,
                            //     //       color: AppColors.white,
                            //     //       size: 11.r,
                            //     //     ),
                            //     //     SizedBox(width: 4.w),
                            //     //     Text(
                            //     //       "${memberCount ?? ""}",
                            //     //       style: TextStyle(
                            //     //         color: AppColors.white,
                            //     //         fontSize: 12.sp,
                            //     //         fontWeight: FontWeight.w300,
                            //     //       ),
                            //     //     ),
                            //     //   ],
                            //     // ),
                            //   ],
                            // ),
                            SizedBox(width: 10.r),
                        
                            /// 编辑群描述按钮
                            if(_controller.isAdminOrOwner()) 
                              GestureDetector(
                                behavior: HitTestBehavior.opaque,
                                onTap: () {
                                  _controller.onEditDescriptionTap();
                                },
                                child: Image.asset(
                                  R.momentEditBtn,
                                  width: 36.r,
                                  height: 36.r,
                                ),
                              ),
                          ],
                        ),
                      ),
                      SizedBox(height: 13.r),
                      
                      /// 群描述
                      Padding(
                        padding: const EdgeInsets.only(left: 78.05).r,
                        child: Obx(
                          () => _controller.description.value != null
                            ? _buildDescSection(
                                desc: _controller.description.value!,
                                onTap: _controller.description.value == null || _controller.description.value!.isEmpty
                                  ? null
                                  : () {
                                    _controller.onDescriptionTap();
                                  }
                              )
                            : SizedBox.shrink(),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescSection({required String desc, Function()? onTap}) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: onTap,      
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          
          /// 描述label
          Row(
            // mainAxisSize: MainAxisSize.min,
            children: [
              Image.asset(R.momentDescIcon, width: 13.33.r, height: 16.r,),
              SizedBox(width: 19.56.r),
              Text(
                L.channel_moment_desc.tr,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  color: AppColors.colorFFCAB692,
                ),
              ),
              // Visibility(
              //   visible: _controller.isAdminOrOwner(),
              //   child: 
              // ),
              // _controller.isAdminOrOwner() 
              //   ? GestureDetector(
              //       behavior: HitTestBehavior.opaque,
              //       onTap: () {
              //         _controller.onEditDescriptionTap();
              //       },
              //       child: Padding(
              //         padding: EdgeInsets.all(8.r),
              //         child: Image.asset(
              //           R.iconEditBtn,
              //           width: 16.r,
              //           height: 16.r,
              //           color: AppColors.white,
              //         ),
              //       ),
              //     )
              //   : SizedBox(height: 30.r),
              
            ],
          ),
          SizedBox(height: 5.5.r),
          
          /// 描述内容
          Text(
            desc.isEmpty ? L.channel_moment_desc_empty.tr : desc,
            maxLines: 4,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              height: 1.4,
              fontSize: 13.sp,
              color: AppColors.colorFFE6E1DD,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return Container(
      width: 1.sw,
      child: _controller.isLoading.value && _controller.moments.isEmpty
          ? _buildBodyLoading()
          : _controller.moments.isEmpty
              ? _buildBodyEmpty()
              : SmartRefresher(
                  controller: _controller.refreshController,
                  enablePullDown: true,
                  enablePullUp: true,
                  onRefresh: () {
                    _controller.onPullToRefresh();
                  },
                  onLoading: () {
                    _controller.onLoadMore();
                  },
                  header: WaterDropMaterialHeader(
                    backgroundColor: AppColors.colorFFF8F8F8,
                    color: AppColors.colorFF3474D0,
                  ),
                  footer: _buildSmartRefreshFooter(),
                  child: ListView.builder(
                    itemCount: _controller.moments.length,
                    physics: BouncingScrollPhysics(),
                    itemBuilder: (context, index) {
                      final moment = _controller.moments[index];
                      return Column(
                        children: [
                          PostWidget(
                            post: moment,
                            isMenuVisible: _controller.isAdminOrOwner(),
                            onPostTap: () {
                              _controller.onPostTap(moment.id);
                            },
                            onMemberProfileTap: () {
                              _controller.onMemberProfileTap(moment.user);
                            },
                            onEditPostTap: () {
                              _controller.onEditPostTap(moment);
                            },
                            onDeletePostTap: () {
                              _controller.onDeletePostTap(moment.id);
                            },
                            onLikeTap: () {
                              _controller.onLikePostTap(moment.id);
                            },
                            onCommentTap: () {
                              _controller.onCommentPostTap(moment.id);
                            },
                            onShareTap: () {
                              _controller.onSharePostTap(moment);
                            },
                            commentSection: _buildCommentSection(moment),
                          ),
                          Divider(),
                        ],
                      );
                    },
                  ),
                ),
    );
  }

  Widget _buildSmartRefreshFooter() {
    return ClassicFooter(
      loadStyle: LoadStyle.ShowWhenLoading,
      textStyle: TextStyle(fontSize: 12.sp),
      noDataText: "- ${L.smart_refresh_no_data.tr} -",
      loadingText: "",
      canLoadingText: L.smart_refresh_can_loading.tr,
      idleText: L.smart_refresh_idle.tr,
      failedText: L.smart_refresh_fail.tr,
      loadingIcon:  Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SpinKitCircle(
            color: AppColors.colorFF3474D0,
            size: 20.r,
          ),
          SizedBox(width: 5.r),
          Text("${L.smart_refresh_loading.tr}...", style: TextStyle(fontSize: 12.sp),),          
        ],
      ),
    );
  }

  Widget? _buildCommentSection(Post post) {
    if (post.commentPaginationModel?.comments == null ||
        post.commentPaginationModel!.comments!.isEmpty) return null;
    return Column(
      children: [
        SizedBox(height: 24.r),
        ..._buildCommentList(post.id, post.commentPaginationModel?.comments),
        if ((post.commentsCount ?? 0) > 2) // 超过2个评论显示view more
          Column(
            children: [
              SizedBox(height: 15.r),
              GestureDetector(
                onTap: () {
                  _controller.onViewMoreCommentTap(post.id);
                },
                child: Text(
                  L.view_more_comments.tr,
                  style: TextStyle(
                    color: AppColors.colorFF3474D0,
                    fontSize: 12.sp,
                  ),
                ),
              ),
            ],
          ),
      ],
    );
  }

  List<CommentWidget> _buildCommentList(
      String? postId, List<Comment>? comments) {
    // 主页最多显示2条评论
    if (comments == null) return [];
    List<Comment> subList = [];
    if (comments.length > 2) {
      subList = [comments[0], comments[1]];
    } else {
      subList = comments;
    }
    return [
      for (var comment in subList)
        CommentWidget(
          isMenuVisible: _controller.isAdminOrOwner(),
          comment: comment,
          onProfileTap: () {
            _controller.onMemberProfileTap(comment.user);
          },
          onDeleteTap: () {
            _controller.onDeleteCommentTap(postId, comment.id);
          },
        )
    ];
  }

  Widget _buildBodyEmpty() {
    return Column(
      children: [
        SizedBox(height: 60.h),
        Image.asset(
          R.iconChannelMomentEmpty,
          width: 40.r,
          height: 40.r,
        ),
        SizedBox(height: 8.h),
        Text(
          L.channel_moment_empty.tr,
          style: TextStyle(
            color: Colors.black.withOpacity(0.5),
            fontSize: 12.sp,
          ),
        ),
      ],
    );
  }

  Widget _buildBodyLoading() {
    return ListView.builder(
      itemCount: 2,
      itemBuilder:(context, index) {
        return ShimmerPostWidget();
      },
    );
  }

  Gradient _getAppBarGradient() {
    return LinearGradient(
      colors: [
        AppColors.primaryBgColor2,
        AppColors.primaryBgColor1,                            
      ],
      // begin: Alignment.topLeft,
      // end: Alignment.bottomRight,
      begin: Alignment(-1.5,-1.5),
      end: Alignment(1,1),      
      // transform: GradientRotation(302),
    );
  }

  BorderRadius _getAppBarBorderRadius() {
    return BorderRadius.only(
      bottomLeft: Radius.circular(30.r),
      bottomRight: Radius.circular(30.r),
    );
  }

}
